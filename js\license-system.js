// نظام إدارة التراخيص المحلي
class LicenseSystem {
    constructor() {
        this.licenseData = this.loadLicenseData();
        this.trialPeriod = 24 * 60 * 60 * 1000; // 24 ساعة بالميلي ثانية
        this.warningPeriod = 30 * 24 * 60 * 60 * 1000; // 30 يوم بالميلي ثانية
        this.lastValidTime = this.getLastValidTime();
        this.initializeSystem();
    }

    // تحميل بيانات الترخيص من localStorage
    loadLicenseData() {
        const defaultData = {
            isActivated: false,
            licenseType: 'none', // none, trial, full
            activationDate: null,
            expirationDate: null,
            customerInfo: null,
            trialStarted: false,
            trialStartDate: null,
            lastWarningDate: null,
            usedLicenseKeys: [] // قائمة المفاتيح المستخدمة
        };
        return JSON.parse(localStorage.getItem('bagFactory_license')) || defaultData;
    }

    // تحميل قائمة المفاتيح المستخدمة
    loadUsedLicenseKeys() {
        const usedKeys = localStorage.getItem('bagFactory_usedKeys');
        return usedKeys ? JSON.parse(usedKeys) : [];
    }

    // حفظ قائمة المفاتيح المستخدمة
    saveUsedLicenseKeys(usedKeys) {
        localStorage.setItem('bagFactory_usedKeys', JSON.stringify(usedKeys));
    }

    // إضافة مفتاح إلى قائمة المستخدمة
    addUsedLicenseKey(licenseKey, licenseId) {
        const usedKeys = this.loadUsedLicenseKeys();
        const keyHash = this.generateKeyHash(licenseKey);

        // التحقق من عدم وجود المفتاح مسبق
        const existingKey = usedKeys.find(key => key.hash === keyHash);
        if (!existingKey) {
            const usageInfo = {
                hash: keyHash,
                licenseId: licenseId,
                usedDate: new Date().toISOString(),
                deviceKey: this.generateDeviceKey(),
                keyPreview: licenseKey.substring(0, 10) + '...' + licenseKey.substring(licenseKey.length - 4),
                userAgent: navigator.userAgent.substring(0, 50),
                status: 'active', // الحالة الافتراضية للمفاتيح الجديدة
                cancelledDate: null,
                reason: null
            };

            usedKeys.push(usageInfo);
            this.saveUsedLicenseKeys(usedKeys);

            console.log('تم تسجيل مفتاح الترخيص كمستخدم:', usageInfo.keyPreview);
            console.log('تاريخ الاستخدام:', usageInfo.usedDate);
        } else {
            console.log('المفتاح موجود مسبقًا في قائمة المستخدمة');
        }
    }

    // التحقق من استخدام المفتاح مسبقًا
    isLicenseKeyUsed(licenseKey) {
        const usedKeys = this.loadUsedLicenseKeys();
        const keyHash = this.generateKeyHash(licenseKey);
        const usedKey = usedKeys.find(key => key.hash === keyHash);

        if (usedKey) {
            console.log('تم العثور على مفتاح مستخدم مسبقًا:');
            console.log('- تاريخ الاستخدام:', usedKey.usedDate);
            console.log('- معرف الترخيص:', usedKey.licenseId);

            if (usedKey.status === 'cancelled') {
                console.log('- حالة المفتاح: تم إلغاء الاشتراك');
                console.log('- تاريخ الإلغاء:', usedKey.cancelledDate);
                console.log('- سبب الإلغاء:', usedKey.reason);
            }

            return usedKey; // إرجاع بيانات المفتاح المستخدم بدلاً من true فقط
        }

        return false;
    }

    // توليد hash للمفتاح
    generateKeyHash(licenseKey) {
        // استخدام hash بسيط للمفتاح
        let hash = 0;
        for (let i = 0; i < licenseKey.length; i++) {
            const char = licenseKey.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return hash.toString();
    }

    // حفظ بيانات الترخيص
    saveLicenseData() {
        localStorage.setItem('bagFactory_license', JSON.stringify(this.licenseData));
    }

    // إلغاء الاشتراك وحذف جميع بيانات الترخيص - محسن للسرعة
    clearLicenseData() {
        console.log('بدء عملية إلغاء الاشتراك المحسنة');

        // التأكد من تسجيل المفتاح الحالي كمستخدم قبل إلغاء الاشتراك
        if (this.licenseData.isActivated && this.licenseData.currentLicenseId) {
            console.log('تسجيل المفتاح الحالي كمستخدم نهائياً قبل إلغاء الاشتراك');

            // البحث عن المفتاح الحالي في قائمة المفاتيح المستخدمة وتحديث حالته
            const usedKeys = this.loadUsedLicenseKeys();
            const currentKeyIndex = usedKeys.findIndex(key => key.licenseId === this.licenseData.currentLicenseId);

            if (currentKeyIndex !== -1) {
                // تحديث المفتاح الحالي ليشير إلى أنه تم إلغاء اشتراكه
                usedKeys[currentKeyIndex].cancelledDate = new Date().toISOString();
                usedKeys[currentKeyIndex].status = 'cancelled';
                usedKeys[currentKeyIndex].reason = 'إلغاء الاشتراك من قبل المستخدم';

                // حفظ قائمة المفاتيح المحدثة
                this.saveUsedLicenseKeys(usedKeys);
                console.log('تم تسجيل إلغاء الاشتراك للمفتاح:', usedKeys[currentKeyIndex].keyPreview);
            }
        }

        // حذف بيانات الترخيص من localStorage فقط (الاحتفاظ بقائمة المفاتيح المستخدمة)
        try {
            localStorage.removeItem('bagFactory_license');
            // لا نحذف bagFactory_usedKeys للاحتفاظ بسجل المفاتيح المستخدمة
            console.log('تم حذف بيانات الترخيص من localStorage مع الاحتفاظ بسجل المفاتيح المستخدمة');
        } catch (error) {
            console.error('خطأ في حذف بيانات الترخيص:', error);
        }

        // إعادة تعيين بيانات الترخيص إلى القيم الافتراضية فور
        this.licenseData = {
            isActivated: false,
            licenseType: null,
            activationDate: null,
            expirationDate: null,
            customerInfo: null,
            trialStarted: false,
            trialStartDate: null,
            lastWarningDate: null,
            usedLicenseKeys: [],
            currentLicenseId: null
        };

        // الخروج الفوري من البرنامج بدون تأخير
        this.forceLogout('تم إلغاء الاشتراك');

        return {
            success: true,
            message: 'تم إلغاء الاشتراك بنجاح'
        };
    }

    // الخروج الفوري من البرنامج - محسن للسرعة
    forceLogout(reason) {
        console.log('تنفيذ الخروج الفوري المحسن:', reason);

        // إيقاف جميع الفحوصات الدورية فور
        if (this.licenseCheckInterval) {
            clearInterval(this.licenseCheckInterval);
        }
        if (this.fullCheckInterval) {
            clearInterval(this.fullCheckInterval);
        }

        // مسح بيانات المستخدم فور
        this.clearUserSession();

        // إخفاء لوحة التحكم وإظهار شاشة تسجيل الدخول فور
        this.switchToLoginScreen();

        // إظهار نافذة الترخيص بدلاً من إعادة التحميل
        setTimeout(() => {
            showLicenseModal();
        }, 500);
    }

    // مسح جلسة المستخدم فور
    clearUserSession() {
        try {
            // مسح بيانات المستخدم من النظام
            if (typeof app !== 'undefined' && app.currentUser) {
                app.currentUser = null;
            }
            if (typeof userManager !== 'undefined' && userManager.currentUser) {
                userManager.currentUser = null;
            }

            // مسح بيانات الجلسة من localStorage
            localStorage.removeItem('bagFactory_currentUser');

            console.log('تم مسح جلسة المستخدم بنجاح');
        } catch (error) {
            console.error('خطأ في مسح جلسة المستخدم:', error);
        }
    }

    // التبديل إلى شاشة تسجيل الدخول فور
    switchToLoginScreen() {
        try {
            const dashboard = document.getElementById('dashboard');
            const mainApp = document.getElementById('mainApp');
            const loginScreen = document.getElementById('loginScreen');

            // إخفاء جميع الشاشات الرئيسية فور
            if (dashboard) {
                dashboard.style.display = 'none';
                dashboard.classList.add('d-none');
            }
            if (mainApp) {
                mainApp.style.display = 'none';
                mainApp.classList.add('d-none');
            }

            // إظهار شاشة تسجيل الدخول فور
            if (loginScreen) {
                loginScreen.style.display = 'block';
                loginScreen.classList.remove('d-none');
            }

            console.log('تم التبديل إلى شاشة تسجيل الدخول');
        } catch (error) {
            console.error('خطأ في التبديل إلى شاشة تسجيل الدخول:', error);
        }
    }

    // إظهار رسالة الخروج
    showLogoutMessage(reason) {
        // لا نحتاج لإظهار رسالة منبثقة، سيتم عرض نافذة الترخيص
        console.log('رسالة الخروج:', reason);
    }

    // إعادة تحميل سريعة - معطلة لمنع إعادة التحميل التلقائي
    performQuickReload() {
        // لا نقوم بإعادة التحميل التلقائي
        console.log('تم تعطيل إعادة التحميل التلقائي');
    }

    // تشفير البيانات (تشفير بسيط للحماية الأساسية)
    encrypt(text, key) {
        let result = '';
        for (let i = 0; i < text.length; i++) {
            result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
        }
        return btoa(result);
    }

    // فك التشفير
    decrypt(encryptedText, key) {
        try {
            const text = atob(encryptedText);
            let result = '';
            for (let i = 0; i < text.length; i++) {
                result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
            }
            return result;
        } catch (e) {
            return null;
        }
    }

    // توليد مفتاح تشفير فريد للجهاز
    generateDeviceKey() {
        const userAgent = navigator.userAgent;
        const language = navigator.language;
        const platform = navigator.platform;
        const screenRes = screen.width + 'x' + screen.height;
        return btoa(userAgent + language + platform + screenRes).substring(0, 32);
    }

    // التحقق من صحة مفتاح الترخيص
    validateLicenseKey(licenseKey) {
        try {
            // التحقق من وجود المفتاح أولاً
            if (!licenseKey || licenseKey.trim() === '') {
                return { valid: false, error: 'يرجى إدخال مفتاح الترخيص' };
            }

            // التحقق من استخدام المفتاح مسبقًا - هذا هو الفحص الأهم
            const usedKeyInfo = this.isLicenseKeyUsed(licenseKey);
            if (usedKeyInfo) {
                console.log('محاولة استخدام مفتاح مستخدم مسبقًا:', licenseKey.substring(0, 10) + '...');

                let errorMessage = 'لقد تم استخدام هذا المفتاح من قبل ومنتهي الصلاحية.\nكل مفتاح ترخيص يمكن استخدامه مرة واحدة فقط.';

                // إضافة معلومات إضافية إذا كان المفتاح تم إلغاء اشتراكه
                if (usedKeyInfo.status === 'cancelled') {
                    const cancelDate = new Date(usedKeyInfo.cancelledDate).toLocaleDateString('ar-EG');
                    errorMessage += `\n\nتم إلغاء الاشتراك لهذا المفتاح في: ${cancelDate}`;
                    errorMessage += '\nلا يمكن إعادة تفعيل المفاتيح الملغاة.';
                } else {
                    const usedDate = new Date(usedKeyInfo.usedDate).toLocaleDateString('ar-EG');
                    errorMessage += `\n\nتم استخدام هذا المفتاح في: ${usedDate}`;
                }

                return {
                    valid: false,
                    error: errorMessage,
                    isUsed: true
                };
            }

            const deviceKey = this.generateDeviceKey();
            const decrypted = this.decrypt(licenseKey, deviceKey);

            if (!decrypted) {
                return { valid: false, error: 'مفتاح الترخيص غير صحيح أو تالف' };
            }

            const licenseData = JSON.parse(decrypted);

            // التحقق من البيانات المطلوبة
            if (!licenseData.customerName || !licenseData.customerPhone ||
                !licenseData.licenseType || !licenseData.expirationDate) {
                return { valid: false, error: 'بيانات الترخيص غير مكتملة أو تالفة' };
            }

            // التحقق من تاريخ الانتهاء
            const expirationDate = new Date(licenseData.expirationDate);
            const currentDate = new Date();

            if (expirationDate < currentDate) {
                return { valid: false, error: 'انتهت صلاحية هذا الترخيص في ' + expirationDate.toLocaleDateString('ar-EG') };
            }

            console.log('مفتاح ترخيص صالح وجديد:', licenseKey.substring(0, 10) + '...');
            return { valid: true, data: licenseData };
        } catch (e) {
            console.error('خطأ في التحقق من مفتاح الترخيص:', e);
            return { valid: false, error: 'خطأ في قراءة مفتاح الترخيص - تأكد من صحة المفتاح' };
        }
    }

    // تفعيل الترخيص
    activateLicense(licenseKey) {
        console.log('محاولة تفعيل مفتاح ترخيص...');

        const validation = this.validateLicenseKey(licenseKey);

        if (!validation.valid) {
            console.log('فشل في تفعيل الترخيص:', validation.error);
            return { success: false, message: validation.error };
        }

        const licenseData = validation.data;
        const currentDate = new Date();

        // تسجيل المفتاح كمستخدم - هذا يضمن عدم إمكانية استخدامه مرة أخرى
        const licenseId = licenseData.licenseId || this.generateLicenseId();
        this.addUsedLicenseKey(licenseKey, licenseId);

        console.log('تم تسجيل المفتاح كمستخدم بنجاح - لن يمكن استخدامه مرة أخرى');

        // حفظ بيانات الترخيص
        this.licenseData = {
            isActivated: true,
            licenseType: licenseData.licenseType,
            activationDate: currentDate.toISOString(),
            expirationDate: licenseData.expirationDate,
            customerInfo: {
                name: licenseData.customerName,
                phone: licenseData.customerPhone,
                email: licenseData.customerEmail || '',
                company: licenseData.customerCompany || ''
            },
            trialStarted: false,
            trialStartDate: null,
            lastWarningDate: null,
            usedLicenseKeys: this.loadUsedLicenseKeys(),
            currentLicenseId: licenseId
        };

        this.saveLicenseData();
        this.updateLicenseUI();
        this.updateDashboardCustomerCard();

        // إلغاء أي عد تنازلي للخروج التلقائي (في حالة التجديد)
        this.cancelAutoLogoutCountdown();

        return {
            success: true,
            message: 'تم تفعيل الترخيص بنجاح',
            licenseType: licenseData.licenseType
        };
    }

    // توليد معرف فريد للترخيص
    generateLicenseId() {
        return 'LIC_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // عرض قائمة المفاتيح المستخدمة (للمطورين فقط)
    showUsedLicenseKeys() {
        const usedKeys = this.loadUsedLicenseKeys();
        console.log('=== قائمة مفاتيح الترخيص المستخدمة ===');
        console.log('عدد المفاتيح المستخدمة:', usedKeys.length);

        usedKeys.forEach((key, index) => {
            console.log(`${index + 1}. المفتاح: ${key.keyPreview || 'غير محدد'}`);
            console.log(`   تاريخ الاستخدام: ${new Date(key.usedDate).toLocaleString('ar-EG')}`);
            console.log(`   معرف الترخيص: ${key.licenseId}`);
            console.log('   ---');
        });

        return usedKeys;
    }

    // مسح قائمة المفاتيح المستخدمة (للمطورين فقط - استخدم بحذر!)
    clearUsedLicenseKeys() {
        if (confirm('تحذير: هذا سيمسح جميع المفاتيح المستخدمة ويسمح بإعادة استخدامها. هل أنت متأكد؟')) {
            localStorage.removeItem('bagFactory_usedKeys');
            console.log('تم مسح قائمة المفاتيح المستخدمة');
            return true;
        }
        return false;
    }

    // بدء العد التنازلي للخروج التلقائي عند انتهاء الاشتراك
    startAutoLogoutCountdown() {
        // منع تشغيل عدة عدادات في نفس الوقت
        if (this.autoLogoutTimer) {
            clearInterval(this.autoLogoutTimer);
            clearTimeout(this.autoLogoutTimeout);
        }

        let countdown = 10; // 10 ثواني
        console.log('بدء العد التنازلي للخروج التلقائي:', countdown, 'ثانية');

        // إظهار إشعار العد التنازلي
        this.showCountdownNotification(countdown);

        // العد التنازلي كل ثانية
        this.autoLogoutTimer = setInterval(() => {
            countdown--;
            console.log('العد التنازلي للخروج:', countdown, 'ثانية');

            // تحديث الإشعار
            this.updateCountdownNotification(countdown);

            if (countdown <= 0) {
                clearInterval(this.autoLogoutTimer);
                this.executeAutoLogout();
            }
        }, 1000);

        // خروج تلقائي بعد 10 ثواني (احتياطي)
        this.autoLogoutTimeout = setTimeout(() => {
            this.executeAutoLogout();
        }, 10000);
    }

    // تنفيذ الخروج التلقائي
    executeAutoLogout() {
        console.log('تنفيذ الخروج التلقائي بسبب انتهاء الاشتراك');

        // مسح العدادات
        if (this.autoLogoutTimer) clearInterval(this.autoLogoutTimer);
        if (this.autoLogoutTimeout) clearTimeout(this.autoLogoutTimeout);

        // إخفاء إشعار العد التنازلي
        this.hideCountdownNotification();

        // تنفيذ الخروج
        this.forceLogout('انتهت صلاحية الاشتراك - تم تسجيل الخروج تلقائ');
    }

    // إلغاء العد التنازلي (في حالة تجديد الترخيص)
    cancelAutoLogoutCountdown() {
        if (this.autoLogoutTimer) {
            clearInterval(this.autoLogoutTimer);
            this.autoLogoutTimer = null;
        }
        if (this.autoLogoutTimeout) {
            clearTimeout(this.autoLogoutTimeout);
            this.autoLogoutTimeout = null;
        }
        this.hideCountdownNotification();
        console.log('تم إلغاء العد التنازلي للخروج التلقائي');
    }

    // بدء الفترة التجريبية
    startTrial() {
        if (this.licenseData.trialStarted) {
            return { success: false, message: 'تم استخدام الفترة التجريبية من قبل' };
        }

        const currentDate = new Date();
        const expirationDate = new Date(currentDate.getTime() + this.trialPeriod);

        this.licenseData = {
            ...this.licenseData,
            isActivated: true,
            licenseType: 'trial',
            activationDate: currentDate.toISOString(),
            expirationDate: expirationDate.toISOString(),
            trialStarted: true,
            trialStartDate: currentDate.toISOString()
        };

        this.saveLicenseData();
        this.updateLicenseUI();
        this.updateDashboardCustomerCard();

        // إلغاء أي عد تنازلي للخروج التلقائي (في حالة بدء فترة تجريبية جديدة)
        this.cancelAutoLogoutCountdown();

        return {
            success: true,
            message: 'تم بدء الفترة التجريبية (24 ساعة)',
            expirationDate: expirationDate
        };
    }

    // التحقق من حالة الترخيص - بدون وضع مجاني
    checkLicenseStatus() {
        // إذا لم يتم تفعيل أي ترخيص، منع الوصول
        if (!this.licenseData.isActivated) {
            return {
                status: 'inactive',
                message: 'يجب تفعيل ترخيص صالح للوصول للنظام',
                allowAccess: false
            };
        }

        // فحص سريع لتاريخ الانتهاء
        if (!this.licenseData.expirationDate) {
            return {
                status: 'inactive',
                message: 'يجب تفعيل ترخيص صالح للوصول للنظام',
                allowAccess: false
            };
        }

        const currentTime = Date.now();
        const expirationTime = new Date(this.licenseData.expirationDate).getTime();
        const timeRemaining = expirationTime - currentTime;

        // فحص فوري لانتهاء الصلاحية - منع الوصول
        if (timeRemaining <= 0) {
            console.log('تم اكتشاف انتهاء صلاحية الترخيص - منع الوصول');

            // إلغاء تفعيل الترخيص فور
            this.licenseData.isActivated = false;

            // حفظ سريع للبيانات
            try {
                this.saveLicenseData();
            } catch (error) {
                console.error('خطأ في حفظ بيانات الترخيص:', error);
            }

            // بدء عد تنازلي للخروج التلقائي
            this.startAutoLogoutCountdown();

            // منع الوصول للنظام
            return {
                status: 'expired',
                message: 'انتهت صلاحية الترخيص - سيتم تسجيل الخروج تلقائ خلال 10 ثواني',
                allowAccess: false,
                autoLogout: true
            };
        }

        // حساب الوقت المتبقي
        const daysRemaining = Math.ceil(timeRemaining / (24 * 60 * 60 * 1000));

        // فحص الفترة التجريبية
        if (this.licenseData.licenseType === 'trial') {
            const hoursRemaining = Math.ceil(timeRemaining / (60 * 60 * 1000));
            return {
                status: 'trial',
                message: `الفترة التجريبية - متبقي ${hoursRemaining} ساعة`,
                timeRemaining: timeRemaining,
                hoursRemaining: hoursRemaining,
                allowAccess: true
            };
        }

        // فحص فترة التحذير (30 يوم)
        if (timeRemaining <= this.warningPeriod) {
            return {
                status: 'warning',
                message: `ينتهي الاشتراك خلال ${daysRemaining} يوم`,
                timeRemaining: timeRemaining,
                daysRemaining: daysRemaining,
                allowAccess: true
            };
        }

        // الترخيص نشط
        return {
            status: 'active',
            message: `مشترك - متبقي ${daysRemaining} يوم`,
            timeRemaining: timeRemaining,
            daysRemaining: daysRemaining,
            allowAccess: true
        };
    }

    // تحديث واجهة المستخدم
    updateLicenseUI() {
        const licenseBtn = document.getElementById('licenseKeyBtn');
        const licenseText = document.getElementById('licenseKeyText');
        
        if (!licenseBtn || !licenseText) return;

        const status = this.checkLicenseStatus();

        // تحديث النص واللون حسب الحالة
        switch (status.status) {
            case 'inactive':
                licenseBtn.className = 'btn btn-outline-warning neumorphic-btn w-100 mt-2';
                licenseText.textContent = 'مفتاح الترخيص';
                break;
            case 'free':
                licenseBtn.className = 'btn btn-outline-primary neumorphic-btn w-100 mt-2';
                licenseText.textContent = 'مجاني';
                break;
            case 'trial':
                licenseBtn.className = 'btn btn-outline-info neumorphic-btn w-100 mt-2';
                licenseText.textContent = 'تجريبي';
                break;
            case 'active':
                licenseBtn.className = 'btn btn-outline-success neumorphic-btn w-100 mt-2';
                licenseText.textContent = 'مشترك';
                break;
            case 'warning':
                licenseBtn.className = 'btn btn-outline-warning neumorphic-btn w-100 mt-2';
                licenseText.textContent = 'ينتهي قريباً';
                break;
            case 'expired':
                licenseBtn.className = 'btn btn-outline-danger neumorphic-btn w-100 mt-2';
                licenseText.textContent = 'منتهي الصلاحية';
                break;
        }

        // تحديث قائمة المفاتيح الملغاة
        this.updateCancelledKeysDisplay();
    }

    // تحديث عرض المفاتيح الملغاة
    updateCancelledKeysDisplay() {
        const cancelledKeysCard = document.getElementById('cancelledKeysCard');
        const cancelledKeysList = document.getElementById('cancelledKeysList');

        if (!cancelledKeysCard || !cancelledKeysList) return;

        const usedKeys = this.loadUsedLicenseKeys();
        const cancelledKeys = usedKeys.filter(key => key.status === 'cancelled');

        if (cancelledKeys.length === 0) {
            cancelledKeysCard.style.display = 'none';
            return;
        }

        cancelledKeysCard.style.display = 'block';

        let html = '';
        cancelledKeys.forEach((key, index) => {
            const cancelDate = new Date(key.cancelledDate).toLocaleDateString('ar-EG');

            html += `
                <div class="border rounded p-2 mb-2 bg-light">
                    <div class="row">
                        <div class="col-8">
                            <small><strong>مفتاح:</strong> ${key.keyPreview}</small><br>
                            <small><strong>تاريخ الإلغاء:</strong> ${cancelDate}</small>
                        </div>
                        <div class="col-4 text-end">
                            <span class="badge bg-danger">ملغي</span>
                        </div>
                    </div>
                </div>
            `;
        });

        cancelledKeysList.innerHTML = html;
    }

    // تحديث نافذة الترخيص
    updateLicenseModal() {
        const status = this.checkLicenseStatus();
        const statusAlert = document.getElementById('licenseStatusAlert');
        const statusTitle = document.getElementById('licenseStatusTitle');
        const statusMessage = document.getElementById('licenseStatusMessage');
        const customerInfoCard = document.getElementById('customerInfoCard');
        const licenseInputCard = document.getElementById('licenseInputCard');
        const trialBtn = document.getElementById('trialBtn');

        if (!statusAlert) return;

        // تحديث حالة الترخيص
        switch (status.status) {
            case 'inactive':
                statusAlert.className = 'alert alert-warning';
                statusTitle.textContent = 'لم يتم التفعيل';
                statusMessage.textContent = 'لم يتم تفعيل أي ترخيص';
                customerInfoCard.style.display = 'none';
                licenseInputCard.style.display = 'block';
                // إظهار زر الفترة التجريبية إذا لم يتم استخدامها
                if (trialBtn) {
                    trialBtn.style.display = this.licenseData.trialStarted ? 'none' : 'inline-block';
                }
                break;
            case 'expired':
                statusAlert.className = 'alert alert-danger';
                statusTitle.textContent = 'انتهت صلاحية الترخيص';
                statusMessage.textContent = 'يجب تجديد الاشتراك للوصول للنظام';
                customerInfoCard.style.display = 'none';
                licenseInputCard.style.display = 'block';
                // إظهار زر الفترة التجريبية إذا لم يتم استخدامها
                if (trialBtn) {
                    trialBtn.style.display = this.licenseData.trialStarted ? 'none' : 'inline-block';
                }
                break;
            case 'trial':
                statusAlert.className = 'alert alert-info';
                statusTitle.textContent = 'فترة تجريبية';
                statusMessage.textContent = status.message;
                customerInfoCard.style.display = 'none';
                licenseInputCard.style.display = 'block';
                // إخفاء زر الفترة التجريبية
                if (trialBtn) {
                    trialBtn.style.display = 'none';
                }
                break;
            case 'active':
                statusAlert.className = 'alert alert-success';
                statusTitle.textContent = 'مشترك';
                statusMessage.textContent = status.message;
                this.updateCustomerInfo();
                customerInfoCard.style.display = 'block';
                licenseInputCard.style.display = 'none';
                // إخفاء زر الفترة التجريبية
                if (trialBtn) {
                    trialBtn.style.display = 'none';
                }
                break;
            case 'warning':
                statusAlert.className = 'alert alert-warning';
                statusTitle.textContent = 'تحذير انتهاء الاشتراك';
                statusMessage.textContent = status.message;
                this.updateCustomerInfo();
                customerInfoCard.style.display = 'block';
                licenseInputCard.style.display = 'block';
                // إخفاء زر الفترة التجريبية
                if (trialBtn) {
                    trialBtn.style.display = 'none';
                }
                break;
            case 'expired':
                statusAlert.className = 'alert alert-danger';
                statusTitle.textContent = 'انتهت الصلاحية';
                statusMessage.textContent = 'انتهت صلاحية الترخيص - يجب تجديد الاشتراك';
                customerInfoCard.style.display = 'none';
                licenseInputCard.style.display = 'block';
                // إخفاء زر الفترة التجريبية إذا تم استخدامها من قبل
                if (trialBtn) {
                    trialBtn.style.display = this.licenseData.trialStarted ? 'none' : 'inline-block';
                }
                break;
        }

        // تحديث قائمة المفاتيح الملغاة
        this.updateCancelledKeysDisplay();
    }

    // تحديث عرض المفاتيح الملغاة
    updateCancelledKeysDisplay() {
        const cancelledKeysCard = document.getElementById('cancelledKeysCard');
        const cancelledKeysList = document.getElementById('cancelledKeysList');

        if (!cancelledKeysCard || !cancelledKeysList) return;

        const usedKeys = this.loadUsedLicenseKeys();
        const cancelledKeys = usedKeys.filter(key => key.status === 'cancelled');

        if (cancelledKeys.length === 0) {
            cancelledKeysCard.style.display = 'none';
            return;
        }

        cancelledKeysCard.style.display = 'block';

        let html = '';
        cancelledKeys.forEach((key, index) => {
            const cancelDate = new Date(key.cancelledDate).toLocaleDateString('ar-EG');
            const cancelTime = new Date(key.cancelledDate).toLocaleTimeString('ar-EG');

            html += `
                <div class="border rounded p-3 mb-2 bg-light">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="text-danger mb-1">
                                <i class="bi bi-key me-2"></i>
                                مفتاح ملغي ${index + 1}
                            </h6>
                            <p class="mb-1"><strong>المعرف:</strong> ${key.keyPreview}</p>
                            <p class="mb-1"><strong>تاريخ الإلغاء:</strong> ${cancelDate} في ${cancelTime}</p>
                            <p class="mb-0"><strong>السبب:</strong> ${key.reason || 'غير محدد'}</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-danger">ملغي</span>
                        </div>
                    </div>
                </div>
            `;
        });

        cancelledKeysList.innerHTML = html;
    }

    // تحديث معلومات العميل
    updateCustomerInfo() {
        if (!this.licenseData.customerInfo) return;

        const customerName = document.getElementById('customerName');
        const customerPhone = document.getElementById('customerPhone');
        const customerEmail = document.getElementById('customerEmail');
        const activationDate = document.getElementById('activationDate');
        const expirationDate = document.getElementById('expirationDate');
        const remainingDays = document.getElementById('remainingDays');

        if (customerName) customerName.textContent = this.licenseData.customerInfo.name || '-';
        if (customerPhone) customerPhone.textContent = this.licenseData.customerInfo.phone || '-';
        if (customerEmail) customerEmail.textContent = this.licenseData.customerInfo.email || '-';

        if (activationDate && this.licenseData.activationDate) {
            const date = new Date(this.licenseData.activationDate);
            activationDate.textContent = date.toLocaleDateString('ar-EG');
        }

        if (expirationDate && this.licenseData.expirationDate) {
            const date = new Date(this.licenseData.expirationDate);
            expirationDate.textContent = date.toLocaleDateString('ar-EG');
        }

        if (remainingDays) {
            const status = this.checkLicenseStatus();
            if (status.daysRemaining !== undefined) {
                remainingDays.textContent = `${status.daysRemaining} يوم`;
            } else if (status.hoursRemaining !== undefined) {
                remainingDays.textContent = `${status.hoursRemaining} ساعة`;
            }
        }

        // تحديث بطاقة العميل في لوحة التحكم
        this.updateDashboardCustomerCard();
    }

    // تحديث بطاقة معلومات العميل في لوحة التحكم
    updateDashboardCustomerCard() {
        const customerCard = document.getElementById('customerLicenseCard');
        if (!customerCard) return;

        const status = this.checkLicenseStatus();

        // إظهار أو إخفاء البطاقة حسب حالة الترخيص
        if (status.status === 'active' || status.status === 'warning' || status.status === 'trial') {
            customerCard.style.display = 'block';

            // تحديث شارة الحالة
            const statusBadge = document.getElementById('licenseStatusBadge');
            if (statusBadge) {
                switch (status.status) {
                    case 'active':
                        statusBadge.className = 'badge bg-success';
                        statusBadge.textContent = 'مشترك';
                        break;
                    case 'trial':
                        statusBadge.className = 'badge bg-info';
                        statusBadge.textContent = 'فترة تجريبية';
                        break;
                    case 'warning':
                        statusBadge.className = 'badge bg-warning';
                        statusBadge.textContent = 'ينتهي قريباً';
                        break;

                }
            }

            // تحديث معلومات العميل في لوحة التحكم
            if (this.licenseData.customerInfo) {
                const dashboardCustomerName = document.getElementById('dashboardCustomerName');
                const dashboardCustomerPhone = document.getElementById('dashboardCustomerPhone');
                const dashboardCustomerEmail = document.getElementById('dashboardCustomerEmail');
                const dashboardActivationDate = document.getElementById('dashboardActivationDate');
                const dashboardExpirationDate = document.getElementById('dashboardExpirationDate');
                const dashboardRemainingDays = document.getElementById('dashboardRemainingDays');
                const dashboardLicenseType = document.getElementById('dashboardLicenseType');

                if (status.status === 'free') {
                    // معلومات افتراضية للوضع المجاني
                    if (dashboardCustomerName) dashboardCustomerName.textContent = 'مستخدم مجاني';
                    if (dashboardCustomerPhone) dashboardCustomerPhone.textContent = '-';
                    if (dashboardCustomerEmail) dashboardCustomerEmail.textContent = '-';
                    if (dashboardActivationDate) dashboardActivationDate.textContent = '-';
                    if (dashboardExpirationDate) dashboardExpirationDate.textContent = 'غير محدود';
                    if (dashboardLicenseType) dashboardLicenseType.textContent = 'مجاني';
                    if (dashboardRemainingDays) {
                        dashboardRemainingDays.textContent = 'غير محدود';
                        dashboardRemainingDays.className = 'fw-bold text-primary';
                    }
                } else if (this.licenseData.customerInfo) {
                    if (dashboardCustomerName) dashboardCustomerName.textContent = this.licenseData.customerInfo.name || '-';
                    if (dashboardCustomerPhone) dashboardCustomerPhone.textContent = this.licenseData.customerInfo.phone || '-';
                    if (dashboardCustomerEmail) dashboardCustomerEmail.textContent = this.licenseData.customerInfo.email || '-';

                    if (dashboardActivationDate && this.licenseData.activationDate) {
                        const date = new Date(this.licenseData.activationDate);
                        dashboardActivationDate.textContent = date.toLocaleDateString('ar-EG');
                    }

                    if (dashboardExpirationDate && this.licenseData.expirationDate) {
                        const date = new Date(this.licenseData.expirationDate);
                        dashboardExpirationDate.textContent = date.toLocaleDateString('ar-EG');
                    }

                    if (dashboardLicenseType) {
                        dashboardLicenseType.textContent = this.licenseData.licenseType === 'trial' ? 'تجريبي' : 'كامل';
                    }

                    if (dashboardRemainingDays) {
                        if (status.daysRemaining !== undefined) {
                            dashboardRemainingDays.textContent = `${status.daysRemaining} يوم`;
                            dashboardRemainingDays.className = status.daysRemaining <= 7 ? 'fw-bold text-danger' : 'fw-bold text-success';
                        } else if (status.hoursRemaining !== undefined) {
                            dashboardRemainingDays.textContent = `${status.hoursRemaining} ساعة`;
                            dashboardRemainingDays.className = 'fw-bold text-warning';
                        }
                    }
                }
            }

            // إظهار/إخفاء زر إلغاء الاشتراك
            const cancelSubscriptionBtn = customerCard.querySelector('button[onclick="showCancelSubscriptionModal()"]');
            if (cancelSubscriptionBtn) {
                // إظهار الزر فقط للتراخيص المفعلة (ليس للفترة التجريبية)
                if (this.licenseData.isActivated && this.licenseData.licenseType !== 'trial') {
                    cancelSubscriptionBtn.style.display = 'inline-block';
                } else {
                    cancelSubscriptionBtn.style.display = 'none';
                }
            }
        } else {
            customerCard.style.display = 'none';
        }
    }

    // التحقق من الوصول للتطبيق - منع الوصول بدون ترخيص صالح
    checkAccess() {
        const status = this.checkLicenseStatus();

        // السماح بالوصول فقط للتراخيص الصالحة
        return {
            allowed: status.allowAccess,
            status: status.status,
            message: status.message
        };
    }

    // عرض تحذير انتهاء الفترة التجريبية
    showTrialExpiredWarning() {
        if (this.licenseData.licenseType === 'trial') {
            const status = this.checkLicenseStatus();
            if (status.status === 'expired' || status.status === 'inactive') {
                setTimeout(() => {
                    // إظهار نافذة الترخيص مع رسالة انتهاء الفترة التجريبية
                    showLicenseModal();

                    // تحديث رسالة النافذة لتظهر انتهاء الفترة التجريبية
                    const statusAlert = document.getElementById('licenseStatusAlert');
                    const statusTitle = document.getElementById('licenseStatusTitle');
                    const statusMessage = document.getElementById('licenseStatusMessage');

                    if (statusAlert && statusTitle && statusMessage) {
                        statusAlert.className = 'alert alert-danger';
                        statusTitle.textContent = 'انتهت الفترة التجريبية';
                        statusMessage.textContent = 'انتهت الفترة التجريبية للبرنامج (24 ساعة). يجب الاشتراك للمتابعة.';
                    }

                    // إظهار إشعار إضافي
                    this.showNotification('انتهت الفترة التجريبية', 'انتهت الفترة التجريبية للبرنامج. يجب الاشتراك للمتابعة.', 'warning');
                }, 1000);
            }
        }
    }

    // عرض تحذير انتهاء الاشتراك
    showExpirationWarning() {
        const status = this.checkLicenseStatus();
        const currentDate = new Date();
        const lastWarning = this.licenseData.lastWarningDate ? new Date(this.licenseData.lastWarningDate) : null;

        // عرض التحذير مرة واحدة يومًا فقط للاشتراكات العادية
        if (status.status === 'warning' && (!lastWarning ||
            currentDate.getTime() - lastWarning.getTime() > 24 * 60 * 60 * 1000)) {

            this.licenseData.lastWarningDate = currentDate.toISOString();
            this.saveLicenseData();

            // إظهار إشعار تحذيري
            this.showNotification(
                'تحذير انتهاء الاشتراك',
                `ينتهي اشتراكك خلال ${status.daysRemaining} يوم. يرجى التجديد قبل انتهاء الصلاحية.`,
                'warning'
            );

            // تحديث بطاقة العميل لإظهار التحذير
            this.updateDashboardCustomerCard();
        }

        // تحذير خاص للفترة التجريبية (كل ساعة في آخر 6 ساعات)
        if (this.licenseData.licenseType === 'trial' && status.status === 'trial') {
            if (status.hoursRemaining <= 6) {
                const lastTrialWarning = localStorage.getItem('lastTrialWarning');
                const currentTime = Date.now();

                if (!lastTrialWarning || currentTime - parseInt(lastTrialWarning) > 60 * 60 * 1000) { // كل ساعة
                    localStorage.setItem('lastTrialWarning', currentTime.toString());

                    this.showNotification(
                        'تحذير انتهاء الفترة التجريبية',
                        `تنتهي الفترة التجريبية خلال ${status.hoursRemaining} ساعة. احصل على ترخيص كامل للمتابعة.`,
                        'warning'
                    );
                }
            }
        }
    }

    // عرض إشعار
    showNotification(title, message, type = 'info') {
        // إنشاء إشعار Bootstrap Toast
        const toastContainer = this.getOrCreateToastContainer();

        const toastId = 'toast_' + Date.now();
        const iconClass = this.getIconForType(type);
        const bgClass = this.getBgClassForType(type);

        const toastHTML = `
            <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi ${iconClass} me-2"></i>
                        <strong>${title}</strong><br>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);

        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: type === 'warning' ? false : true,
            delay: type === 'warning' ? 0 : 5000
        });

        toast.show();

        // إزالة العنصر بعد الإخفاء
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });

        // Fallback للمتصفحات القديمة أو Electron
        if (window.electronAPI && window.electronAPI.showNotification) {
            window.electronAPI.showNotification(title, message);
        }
    }

    // الحصول على أو إنشاء حاوية الإشعارات
    getOrCreateToastContainer() {
        let container = document.getElementById('toastContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    }

    // الحصول على أيقونة حسب نوع الإشعار
    getIconForType(type) {
        switch (type) {
            case 'success': return 'bi-check-circle-fill';
            case 'warning': return 'bi-exclamation-triangle-fill';
            case 'danger': return 'bi-x-circle-fill';
            case 'info': return 'bi-info-circle-fill';
            default: return 'bi-info-circle-fill';
        }
    }

    // الحصول على فئة الخلفية حسب نوع الإشعار
    getBgClassForType(type) {
        switch (type) {
            case 'success': return 'bg-success';
            case 'warning': return 'bg-warning';
            case 'danger': return 'bg-danger';
            case 'info': return 'bg-info';
            default: return 'bg-primary';
        }
    }

    // إظهار إشعار العد التنازلي
    showCountdownNotification(countdown) {
        const toastContainer = this.getOrCreateToastContainer();

        const countdownToastHTML = `
            <div id="countdownToast" class="toast align-items-center text-white bg-danger border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="false">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <strong>انتهت صلاحية الاشتراك!</strong><br>
                        <span id="countdownText">سيتم تسجيل الخروج تلقائ خلال ${countdown} ثانية</span>
                    </div>
                </div>
            </div>
        `;

        // إزالة أي إشعار عد تنازلي موجود
        const existingToast = document.getElementById('countdownToast');
        if (existingToast) {
            existingToast.remove();
        }

        toastContainer.insertAdjacentHTML('beforeend', countdownToastHTML);
    }

    // تحديث إشعار العد التنازلي
    updateCountdownNotification(countdown) {
        const countdownText = document.getElementById('countdownText');
        if (countdownText) {
            if (countdown > 0) {
                countdownText.textContent = `سيتم تسجيل الخروج تلقائ خلال ${countdown} ثانية`;
            } else {
                countdownText.textContent = 'جاري تسجيل الخروج...';
            }
        }
    }

    // إخفاء إشعار العد التنازلي
    hideCountdownNotification() {
        const countdownToast = document.getElementById('countdownToast');
        if (countdownToast) {
            countdownToast.remove();
        }
    }

    // تهيئة النظام
    initializeSystem() {
        // فحص التلاعب في الوقت فور التهيئة
        if (this.detectTimeManipulation()) {
            this.handleTimeManipulation();
            return;
        }

        // فحص سلامة البيانات المحفوظة
        if (!this.validateStoredData()) {
            console.error('تم اكتشاف تلاعب في بيانات الترخيص المحفوظة');
            this.handleDataTampering();
            return;
        }

        // حفظ الوقت الحالي كوقت صحيح عند البداية
        this.saveValidTime(Date.now());

        // إضافة حماية ضد تعديل localStorage
        this.protectLocalStorage();

        // تحديث واجهة المستخدم عند التحميل
        setTimeout(() => {
            this.updateLicenseUI();
            this.updateDashboardCustomerCard();
            this.showTrialExpiredWarning();
            this.showExpirationWarning();
        }, 500);

        // فحص دوري سريع كل 30 ثانية للتحقق من التلاعب في الوقت
        this.timeCheckInterval = setInterval(() => {
            if (this.detectTimeManipulation()) {
                this.handleTimeManipulation();
            } else {
                this.saveValidTime(Date.now());
            }
        }, 30000);

        // فحص دوري معتدل كل دقيقة للتحقق من حالة الترخيص
        this.licenseCheckInterval = setInterval(() => {
            this.performQuickLicenseCheck();
        }, 60000);

        // فحص دوري شامل كل 5 دقائق
        this.fullCheckInterval = setInterval(() => {
            this.updateLicenseUI();
            this.updateDashboardCustomerCard();
            this.showExpirationWarning();
        }, 300000);

        // فحص معتدل عند العودة للنافذة فقط
        window.addEventListener('focus', () => {
            // فحص مرة واحدة فقط عند العودة للنافذة
            setTimeout(() => {
                this.performQuickLicenseCheck();
            }, 1000);
        });
    }

    // فحص سريع لحالة الترخيص - مع خروج تلقائي عند انتهاء الاشتراك
    performQuickLicenseCheck() {
        const status = this.checkLicenseStatus();

        // تحديث الواجهة
        this.updateLicenseUI();

        // التعامل مع انتهاء الاشتراك
        if (status.status === 'expired' && status.autoLogout) {
            console.log('تم اكتشاف انتهاء الاشتراك - بدء العد التنازلي للخروج');
            // العد التنازلي سيبدأ من checkLicenseStatus
            return;
        }

        // إظهار تحذيرات للتراخيص التي تنتهي قريباً
        if (status.status === 'warning' || status.status === 'trial') {
            this.showExpirationWarning();
        }
    }

    // الحصول على آخر وقت صحيح محفوظ
    getLastValidTime() {
        const stored = localStorage.getItem('bagFactory_lastValidTime');
        return stored ? parseInt(stored) : Date.now();
    }

    // حفظ الوقت الصحيح
    saveValidTime(time) {
        localStorage.setItem('bagFactory_lastValidTime', time.toString());
        this.lastValidTime = time;
    }

    // التحقق من التلاعب في الوقت
    detectTimeManipulation() {
        const currentTime = Date.now();
        const timeDifference = currentTime - this.lastValidTime;

        // إذا كان الوقت الحالي أقل من آخر وقت صحيح (تم إرجاع الساعة)
        if (timeDifference < -60000) { // هامش دقيقة واحدة فقط للسماح بتعديلات طفيفة
            console.error('تم اكتشاف تلاعب في الوقت - تم إرجاع الساعة');
            console.error('الوقت الحالي:', new Date(currentTime));
            console.error('آخر وقت صحيح:', new Date(this.lastValidTime));
            console.error('الفرق:', Math.abs(timeDifference / 1000 / 60), 'دقيقة');
            return true;
        }

        // إذا كان الفرق كبير جداً (تم تقديم الساعة بشكل مفرط)
        if (timeDifference > 3600000) { // أكثر من ساعة واحدة
            console.error('تم اكتشاف قفزة كبيرة في الوقت - تلاعب محتمل');
            console.error('الفرق:', timeDifference / 1000 / 60 / 60, 'ساعة');
            return true;
        }

        // فحص إضافي: مقارنة مع وقت الخادم إذا كان متاحاً
        if (this.serverTime && Math.abs(currentTime - this.serverTime) > 300000) { // 5 دقائق
            console.error('تم اكتشاف اختلاف كبير مع وقت الخادم');
            return true;
        }

        return false;
    }

    // التعامل مع التلاعب في الوقت
    handleTimeManipulation() {
        console.error('تنفيذ إجراءات مكافحة التلاعب في الوقت');
        
        // إلغاء الترخيص فور
        this.licenseData.isActivated = false;
        this.licenseData.licenseType = 'tampered';
        this.licenseData.tamperedDate = new Date().toISOString();
        this.saveLicenseData();
        
        // حذف الوقت المحفوظ لمنع المحاولات المستقبلية
        localStorage.removeItem('bagFactory_lastValidTime');
        
        // الخروج الفوري
        this.forceLogout('تم اكتشاف تلاعب في وقت النظام');
        
        // إظهار رسالة تحذير قوية
        setTimeout(() => {
            alert('⚠️ تحذير أمني\n\nتم اكتشاف تلاعب في وقت النظام.\nتم إلغاء الترخيص لأسباب أمنية.\n\nيرجى عدم تغيير وقت النظام أثناء استخدام البرنامج.');
        }, 100);
    }

    // فحص الوقت وتحديثه
    validateAndUpdateTime() {
        // فحص التلاعب أولاً
        if (this.detectTimeManipulation()) {
            this.handleTimeManipulation();
            return false;
        }
        
        // حفظ الوقت الحالي كوقت صحيح
        this.saveValidTime(Date.now());
        return true;
    }

    // تحديث فحص حالة الترخيص
    checkLicenseStatus() {
        // فحص التلاعب في الوقت أولاً
        if (!this.validateAndUpdateTime()) {
            return {
                status: 'tampered',
                message: 'تم اكتشاف تلاعب في وقت النظام',
                allowAccess: false
            };
        }

        // إذا لم يتم تفعيل أي ترخيص، منع الوصول
        if (!this.licenseData.isActivated) {
            return {
                status: 'inactive',
                message: 'يجب تفعيل ترخيص صالح للوصول للنظام',
                allowAccess: false
            };
        }

        // فحص سريع لتاريخ الانتهاء
        if (!this.licenseData.expirationDate) {
            return {
                status: 'inactive',
                message: 'يجب تفعيل ترخيص صالح للوصول للنظام',
                allowAccess: false
            };
        }

        const currentTime = Date.now();
        const expirationTime = new Date(this.licenseData.expirationDate).getTime();
        const timeRemaining = expirationTime - currentTime;

        // فحص فوري لانتهاء الصلاحية - منع الوصول
        if (timeRemaining <= 0) {
            console.log('تم اكتشاف انتهاء صلاحية الترخيص - منع الوصول');

            // إلغاء تفعيل الترخيص فور
            this.licenseData.isActivated = false;

            // حفظ سريع للبيانات
            try {
                this.saveLicenseData();
            } catch (error) {
                console.error('خطأ في حفظ بيانات الترخيص:', error);
            }

            // بدء عد تنازلي للخروج التلقائي
            this.startAutoLogoutCountdown();

            // منع الوصول للنظام
            return {
                status: 'expired',
                message: 'انتهت صلاحية الترخيص - سيتم تسجيل الخروج تلقائ خلال 10 ثواني',
                allowAccess: false,
                autoLogout: true
            };
        }

        // حساب الوقت المتبقي
        const daysRemaining = Math.ceil(timeRemaining / (24 * 60 * 60 * 1000));

        // فحص الفترة التجريبية
        if (this.licenseData.licenseType === 'trial') {
            const hoursRemaining = Math.ceil(timeRemaining / (60 * 60 * 1000));
            return {
                status: 'trial',
                message: `الفترة التجريبية - متبقي ${hoursRemaining} ساعة`,
                timeRemaining: timeRemaining,
                hoursRemaining: hoursRemaining,
                allowAccess: true
            };
        }

        // فحص فترة التحذير (30 يوم)
        if (timeRemaining <= this.warningPeriod) {
            return {
                status: 'warning',
                message: `ينتهي الاشتراك خلال ${daysRemaining} يوم`,
                timeRemaining: timeRemaining,
                daysRemaining: daysRemaining,
                allowAccess: true
            };
        }

        // الترخيص نشط
        return {
            status: 'active',
            message: `مشترك - متبقي ${daysRemaining} يوم`,
            timeRemaining: timeRemaining,
            daysRemaining: daysRemaining,
            allowAccess: true
        };
    }

    // تحديث الفحص الدوري
    performQuickLicenseCheck() {
        // فحص التلاعب في الوقت في كل فحص دوري
        if (!this.validateAndUpdateTime()) {
            return; // سيتم التعامل مع التلاعب في validateAndUpdateTime
        }

        const status = this.checkLicenseStatus();

        // تحديث واجهة المستخدم
        this.updateLicenseUI();
        this.updateDashboardCustomerCard();

        if (status.status === 'expired' && status.autoLogout) {
            console.log('تم اكتشاف انتهاء الاشتراك - بدء العد التنازلي للخروج');
            return;
        }

        if (status.status === 'warning' || status.status === 'trial') {
            this.showExpirationWarning();
        }
    }
}

// إنشاء مثيل من نظام التراخيص
const licenseSystem = new LicenseSystem();

// الدوال العامة للاستخدام في HTML
function showLicenseModal() {
    licenseSystem.updateLicenseModal();
    const modal = new bootstrap.Modal(document.getElementById('licenseModal'));
    modal.show();
}

function activateLicense() {
    const licenseKey = document.getElementById('licenseKey').value.trim();
    
    if (!licenseKey) {
        alert('يرجى إدخال مفتاح الترخيص');
        return;
    }

    const result = licenseSystem.activateLicense(licenseKey);
    
    if (result.success) {
        alert(result.message);
        document.getElementById('licenseKey').value = '';
        licenseSystem.updateLicenseModal();
        
        // إغلاق النافذة بعد التفعيل الناجح
        const modal = bootstrap.Modal.getInstance(document.getElementById('licenseModal'));
        if (modal) modal.hide();
    } else {
        alert('خطأ في التفعيل: ' + result.message);
    }
}

function startTrialPeriod() {
    const result = licenseSystem.startTrial();

    if (result.success) {
        alert(result.message);
        licenseSystem.updateLicenseModal();
    } else {
        alert('خطأ: ' + result.message);
    }
}

// عرض نافذة تأكيد إلغاء الاشتراك
function showCancelSubscriptionModal() {
    // التأكد من وجود ترخيص مفعل
    if (!licenseSystem.licenseData.isActivated) {
        alert('لا يوجد اشتراك مفعل لإلغائه');
        return;
    }

    // إعادة تعيين حقل التأكيد
    document.getElementById('cancelConfirmationInput').value = '';
    document.getElementById('confirmCancelBtn').disabled = true;

    // عرض النافذة
    const modal = new bootstrap.Modal(document.getElementById('cancelSubscriptionModal'));
    modal.show();
}

// التحقق من كلمة التأكيد وتفعيل الزر
document.addEventListener('DOMContentLoaded', function() {
    const confirmationInput = document.getElementById('cancelConfirmationInput');
    const confirmBtn = document.getElementById('confirmCancelBtn');

    if (confirmationInput && confirmBtn) {
        confirmationInput.addEventListener('input', function() {
            const inputValue = this.value.trim();
            confirmBtn.disabled = inputValue !== 'إلغاء';
        });
    }
});

// تأكيد إلغاء الاشتراك
function confirmCancelSubscription() {
    const confirmationInput = document.getElementById('cancelConfirmationInput');

    if (confirmationInput.value.trim() !== 'إلغاء') {
        alert('يرجى كتابة كلمة "إلغاء" للتأكيد');
        return;
    }

    console.log('بدء عملية إلغاء الاشتراك');

    // إغلاق النافذة أولاً
    const modal = bootstrap.Modal.getInstance(document.getElementById('cancelSubscriptionModal'));
    if (modal) {
        modal.hide();
        console.log('تم إغلاق نافذة التأكيد');
    }

    // تنفيذ إلغاء الاشتراك مع الخروج الفوري
    try {
        const result = licenseSystem.clearLicenseData();
        console.log('نتيجة إلغاء الاشتراك:', result);

        if (!result.success) {
            alert('حدث خطأ أثناء إلغاء الاشتراك: ' + result.message);
        }
    } catch (error) {
        console.error('خطأ في إلغاء الاشتراك:', error);
        alert('حدث خطأ أثناء إلغاء الاشتراك');

        // خروج فوري حتى لو حدث خطأ
        forceImmediateLogout('حدث خطأ - تم إلغاء الاشتراك');
    }
}

// دالة خروج فوري إضافية للتأكد - بدون إعادة تحميل
function forceImmediateLogout(reason) {
    console.log('تنفيذ الخروج الفوري الإضافي:', reason);

    // إخفاء جميع العناصر الرئيسية
    const mainApp = document.getElementById('mainApp');
    const loginScreen = document.getElementById('loginScreen');

    if (mainApp) {
        mainApp.style.display = 'none';
        mainApp.classList.add('d-none');
    }
    if (loginScreen) {
        loginScreen.style.display = 'block';
        loginScreen.classList.remove('d-none');
    }

    // مسح جميع البيانات
    localStorage.removeItem('bagFactory_license');
    if (typeof app !== 'undefined' && app.currentUser) {
        app.currentUser = null;
    }

    // إظهار نافذة الترخيص بدلاً من رسالة منبثقة
    setTimeout(() => {
        showLicenseModal();
    }, 500);
}




